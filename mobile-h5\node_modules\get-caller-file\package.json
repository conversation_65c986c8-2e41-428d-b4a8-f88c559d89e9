{"name": "get-caller-file", "version": "2.0.5", "description": "", "main": "index.js", "directories": {"test": "tests"}, "files": ["index.js", "index.js.map", "index.d.ts"], "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "@types/node": "^11.10.5", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "engines": {"node": "6.* || 8.* || >= 10.*"}}