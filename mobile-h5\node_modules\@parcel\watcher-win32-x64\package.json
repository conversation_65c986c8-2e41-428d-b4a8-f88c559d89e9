{"name": "@parcel/watcher-win32-x64", "version": "2.5.1", "main": "watcher.node", "repository": {"type": "git", "url": "https://github.com/parcel-bundler/watcher.git"}, "description": "A native C++ Node module for querying and subscribing to filesystem events. Used by Parcel 2.", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "files": ["watcher.node"], "engines": {"node": ">= 10.0.0"}, "os": ["win32"], "cpu": ["x64"]}